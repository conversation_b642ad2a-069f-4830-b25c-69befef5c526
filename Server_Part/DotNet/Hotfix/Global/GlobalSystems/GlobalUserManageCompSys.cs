using System;
using System.Collections.Generic;
using System.Linq;
using MongoDB.Driver;

namespace MaoYouJi
{
  [FriendOf(typeof(GlobalManageComp))]
  public static partial class GlobalUserManageCompSys
  {
    public static async ETTask RankUserByAttackNum(this GlobalManageComp self)
    {
      try
      {
        // 从数据库获取排名前100的用户，按攻击力降序排序
        DBManagerComponent dbManagerComponent = self.GetParent<Scene>().GetComponent<DBManagerComponent>();
        DBComponent dbComponent = dbManagerComponent.GetMyZoneDB();

        // 由于ET框架的存储结构，无法直接在数据库层面按组件字段排序
        // 因此先获取所有用户数据，然后在内存中排序
        var collection = dbComponent.GetCollection<User>();
        List<User> users = await collection.Find(FilterDefinition<User>.Empty).ToListAsync();

        // 获取在线用户
        var onlineUsers = GlobalInfoCache.Instance.allOnlineUserCache.Values;

        // 使用Dictionary来处理重复用户，key为userId，保证同一用户只保留最新的战斗力数据
        Dictionary<long, RankItem> rankMap = new Dictionary<long, RankItem>();

        long minAttackVal = 0;

        // 处理数据库中的用户
        foreach (User user in users)
        {
          AttackComponent attackComponent = user.GetComponent<AttackComponent>();
          if (attackComponent == null) continue;

          RankItem rankItem = new RankItem();
          rankItem.userId = user.Id;
          rankItem.name = user.nickname;
          rankItem.attackVal = attackComponent.attackNum;
          rankItem.skinId = attackComponent.NowSkin;

          if (minAttackVal == 0 || minAttackVal > attackComponent.attackNum)
          {
            minAttackVal = attackComponent.attackNum;
          }
          rankMap[user.Id] = rankItem;
        }

        // 处理在线用户，如果在线用户的战斗力更高，则更新排名信息
        foreach (User onlineUser in onlineUsers)
        {
          AttackComponent attackComponent = onlineUser.GetComponent<AttackComponent>();
          if (attackComponent == null) continue;

          long realAttackVal = attackComponent.attackNum;

          // 大逃杀活动的特殊处理逻辑
          if (onlineUser.activityName == ActNameEnum.Da_TaoSha)
          {
            UserDaTaoShaInfoComp daTaoShaUserInfo = onlineUser.GetComponent<UserDaTaoShaInfoComp>();
            // TODO: 实现备份攻击力的获取逻辑
            if (daTaoShaUserInfo != null)
            {
              realAttackVal = daTaoShaUserInfo.attackDaoInfo.attackNum;
            }
          }

          if (rankMap.TryGetValue(onlineUser.Id, out RankItem existingItem))
          {
            if (existingItem.attackVal < realAttackVal)
            {
              existingItem.attackVal = realAttackVal;
            }
          }
          else if (realAttackVal > minAttackVal)
          {
            RankItem rankItem = new RankItem();
            rankItem.userId = onlineUser.Id;
            rankItem.name = onlineUser.nickname;
            rankItem.attackVal = realAttackVal;
            rankItem.skinId = attackComponent.NowSkin;
            rankMap[onlineUser.Id] = rankItem;
          }
        }

        // 将Dictionary转换为List并按战斗力排序
        List<RankItem> rankItems = rankMap.Values.ToList();
        rankItems.Sort((a, b) => b.attackVal.CompareTo(a.attackVal));

        ETLog.Info($"rankUserByAttackVal count: {rankItems.Count}");

        // 只保留前100名
        if (rankItems.Count > 100)
        {
          rankItems = rankItems.Take(100).ToList();
        }

        // 移除战斗力为0的用户
        rankItems.RemoveAll(item => item.attackVal == 0);

        // 保存排行榜信息
        RankInfo rankInfo = new RankInfo();
        rankInfo.id = 8888;
        rankInfo.items = rankItems;
        rankInfo.updateTime = TimeInfo.Instance.ServerNow();

        // 更新GlobalManageComp中的排行榜信息
        self.rankInfo = rankInfo;

        // 保存到数据库
        var filter = Builders<RankInfo>.Filter.Eq(r => r.id, rankInfo.id);
        await dbComponent.SaveClass(filter, rankInfo);

        ETLog.Info($"排行榜更新完成，共{rankItems.Count}名用户");
      }
      catch (Exception e)
      {
        ETLog.Error($"更新用户战斗力排行榜失败: {e}");
      }
    }
  }
}